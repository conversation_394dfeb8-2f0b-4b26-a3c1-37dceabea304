<script lang="ts" setup>
import { defineEmits, defineProps } from 'vue';
import { faX } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import DefaultButton from './DefaultButton.vue';

interface Props {
  title: string;
  confirmText?: string;
  canConfirm?: boolean;
}

withDefaults(defineProps<Props>(), {
  confirmText: 'Bestätigen',
  canConfirm: true,
});

const emit = defineEmits(['close', 'confirm']);

function handleConfirm(): void {
  emit('confirm');
}
</script>

<template>
  <div
    aria-modal="true"
    class="fixed inset-0 z-50 flex items-center justify-center bg-gray-800/50 p-4"
    role="dialog"
    @click.prevent="emit('close')"
  >
    <div class="bg-white rounded-2xl shadow-xl w-full max-w-lg p-6" @click.stop>
      <header>
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-bold">{{ title }}</h2>
          <FontAwesomeIcon :icon="faX" class="text-gray-400" @click.prevent="emit('close')" />
        </div>
        <div class="border-t border-gray-300 my-3 -mx-6"></div>
      </header>

      <main class="my-6">
        <slot name="main-content" />
      </main>

      <footer class="flex flex-wrap gap-4 justify-between">
        <DefaultButton class="flex-1 bg-white border border-bamboo-600 text-bamboo-600" @click.prevent="emit('close')">
          Abbrechen
        </DefaultButton>

        <DefaultButton
          :disabled="!canConfirm"
          class="flex-1 bg-gradient-bamboo text-white disabled:opacity-50"
          @click.prevent="handleConfirm"
        >
          {{ confirmText }}
        </DefaultButton>
      </footer>
    </div>
  </div>
</template>
